"""
Gmail Auto Registration - Stealth Mode
Using Playwright with stealth mode to avoid detection
"""

import asyncio
import sys
import json
import click
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any
from playwright.async_api import async_playwright, <PERSON><PERSON>er, Page
from dotenv import load_dotenv
import random
import time
import logging

# Import our modules
from config import Config
from user_data_generator import UserDataGenerator
from proxy_manager import ProxyManager
from browser_profile import BrowserProfileGenerator
from human_behavior import HumanBehavior
from constants import Messages, Timeouts, Paths


class GmailRegistrationOrchestrator:
    """Main orchestrator cho toàn bộ Gmail registration system"""
    
    def __init__(self):
        self.config = Config
        self.proxy_manager = None
        self.user_generator = UserDataGenerator()
        self.browser_generator = BrowserProfileGenerator()
        self.results = []
        load_dotenv()
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('registration.log'),
                logging.StreamHandler()
            ]
        )
        
    async def initialize(self) -> bool:
        """
        Initialize toàn bộ system
        
        Returns:
            bool: True nếu initialization thành công
        """
        try:
            print(Messages.CONSOLE["init_start"])
            
            # Ensure directories exist
            self.config.ensure_directories()
            
            # Validate configuration
            if not self.config.validate_config():
                return False
            
            # Force disable proxy for testing
            self.config.USE_PROXY = False

            # Initialize proxy manager
            if self.config.USE_PROXY:
                self.proxy_manager = ProxyManager()
                if not await self.proxy_manager.initial_setup():
                    print(Messages.CONSOLE["proxy_failed"])
                    return False
            else:
                print(Messages.CONSOLE["proxy_disabled"])
            
            # Print configuration summary
            self.config.print_config_summary()
            
            print(Messages.CONSOLE["init_success"])
            return True
            
        except Exception as e:
            print(f"{Messages.CONSOLE['init_failed']}: {e}")
            return False
    
    async def register_single_account(self, account_id: int = 1) -> Dict[str, Any]:
        """
        Đăng ký một tài khoản Gmail
        
        Args:
            account_id: ID của tài khoản (for logging)
            
        Returns:
            Dict chứa kết quả đăng ký
        """
        start_time = datetime.now()
        print(f"\n{Messages.CONSOLE['registration_start']} #{account_id}")
        
        try:
            # Generate user data
            user_data = self.user_generator.generate_user_info()
            print(f"{Messages.CONSOLE['user_generated']}: {user_data['full_name']} ({user_data['username']})")
            
            # Get proxy if enabled
            proxy_config = None
            if self.proxy_manager:
                proxy_config = await self.proxy_manager.get_working_proxy()
                if not proxy_config:
                    return {
                        "success": False,
                        "error": Messages.ERRORS["no_proxy"],
                        "account_id": account_id,
                        "user_data": user_data,
                        "timestamp": start_time.isoformat()
                    }
                print(f"{Messages.CONSOLE['proxy_using']}: {proxy_config.server}")
            
            # Create browser profile
            browser_profile = self.browser_generator.create_human_browser_profile(proxy_config)
            print(f"🌍 Browser timezone: {browser_profile['timezone_id']}")
            print(f"📱 Viewport: {browser_profile['viewport']}")
            
            # Start registration process
            result = await self._perform_registration(user_data, browser_profile, account_id)
            
            # Calculate duration
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            result["duration_seconds"] = duration
            result["account_id"] = account_id
            
            if result["success"]:
                print(f"{Messages.CONSOLE['registration_success']} #{account_id} in {duration:.1f}s")
                print(f"📧 Email: {result.get('email', 'N/A')}")
            else:
                print(f"{Messages.CONSOLE['registration_failed']} #{account_id}: {result.get('error', Messages.ERRORS['unknown_error'])}")
            
            return result
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "account_id": account_id,
                "timestamp": start_time.isoformat(),
                "duration_seconds": (datetime.now() - start_time).total_seconds()
            }
    
    def _get_month_mapping(self) -> Dict[str, str]:
        """Get month mapping for different locales and formats"""
        return {
            # Standard month names
            "January": "January",
            "February": "February", 
            "March": "March",
            "April": "April",
            "May": "May",
            "June": "June",
            "July": "July",
            "August": "August",
            "September": "September",
            "October": "October",
            "November": "November",
            "December": "December",
            # Abbreviated month names
            "Jan": "January",
            "Feb": "February",
            "Mar": "March",
            "Apr": "April",
            "Jun": "June",
            "Jul": "July",
            "Aug": "August",
            "Sep": "September",
            "Oct": "October",
            "Nov": "November",
            "Dec": "December",
            # Numeric month values
            "1": "January",
            "2": "February",
            "3": "March",
            "4": "April",
            "5": "May",
            "6": "June",
            "7": "July",
            "8": "August",
            "9": "September",
            "10": "October",
            "11": "November",
            "12": "December"
        }
    
    async def _perform_registration(self, user_data: Dict[str, Any], browser_profile: Dict[str, Any], account_id: int) -> Dict[str, Any]:
        """
        Thực hiện quá trình đăng ký thực sự bằng Playwright
        
        Args:
            user_data: User data dict
            browser_profile: Browser profile dict
            account_id: Account ID
            
        Returns:
            Registration result dict
        """
        try:
            print(Messages.CONSOLE["browser_start"])
            
            async with async_playwright() as p:
                # Launch browser with stealth mode
                browser = await p.chromium.launch(
                    headless=False,  # Set to True for production
                    proxy=browser_profile.get('proxy'),
                    args=[
                        '--disable-blink-features=AutomationControlled',
                        '--disable-features=IsolateOrigins,site-per-process',
                        '--disable-site-isolation-trials',
                        f'--window-size={browser_profile["viewport"]["width"]},{browser_profile["viewport"]["height"]}',
                        '--start-maximized'
                    ]
                )
                
                # Create new context with custom viewport and timezone
                context = await browser.new_context(
                    viewport=browser_profile['viewport'],
                    timezone_id=browser_profile['timezone_id'],
                    user_agent=browser_profile['user_agent'],
                    locale=browser_profile['locale'],
                    geolocation=browser_profile.get('geolocation'),
                    permissions=['geolocation']
                )
                
                # Add stealth scripts
                await context.add_init_script("""
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => undefined
                    });
                    Object.defineProperty(navigator, 'languages', {
                        get: () => ['en-US', 'en']
                    });
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5]
                    });
                """)
                
                # Create new page
                page = await context.new_page()
                
                try:
                    # Step 1: Navigate to Gmail signup page
                    logging.info("Step 1: Navigating to signup page")
                    await page.goto('https://accounts.google.com/signup')
                    await self._take_screenshot(page, account_id, "step1")
                    await self._random_delay()
                    
                    # Step 2: Fill basic information
                    logging.info("Step 2: Filling basic information")
                    
                    # Fill First name
                    await page.get_by_role('textbox', name='First name').fill(user_data['first_name'])
                    await self._random_delay()
                    
                    # Fill Last name
                    await page.get_by_role('textbox', name='Last name (optional)').fill(user_data['last_name'])
                    await self._random_delay()
                    
                    # Click Next
                    await page.get_by_role('button', name='Next').click()
                    await self._take_screenshot(page, account_id, "step2")
                    await self._random_delay()
                    
                    # Step 3: Fill personal information
                    logging.info("Step 3: Filling personal information")
                    
                    # Select month
                    month_mapping = self._get_month_mapping()
                    user_month = month_mapping.get(str(user_data['birth_month']), str(user_data['birth_month']))
                    await page.get_by_role('combobox', name='Month').click()
                    await page.get_by_role('option', name=user_month).click()
                    await self._random_delay()
                    
                    # Enter day
                    await page.get_by_role('textbox', name='Day').fill(str(user_data['birth_day']))
                    await self._random_delay()
                    
                    # Enter year
                    await page.get_by_role('textbox', name='Year').fill(str(user_data['birth_year']))
                    await self._random_delay()
                    
                    # Select gender
                    await page.get_by_role('combobox', name='Gender').click()
                    await self._random_delay()
                    
                    # Click on gender option with exact match
                    gender_option = await page.get_by_role('option', name=user_data['gender'], exact=True).click()
                    await self._random_delay()
                    
                    # Click Next
                    await page.get_by_role('button', name='Next').click()
                    await self._take_screenshot(page, account_id, "step3")
                    await self._random_delay()
                    
                    # Step 4: Fill username and password
                    logging.info("Step 4: Filling username and password")
                    
                    # Enter username
                    await page.get_by_label('Username').fill(user_data['username'])
                    await self._random_delay()
                    
                    # Check for username taken error
                    error_text = await page.get_by_text('That username is taken. Try another.').is_visible()
                    if error_text:
                        logging.info("Username taken, selecting suggested username")
                        suggested_username = await page.get_by_role('button', name=lambda n: n.startswith('jd')).text_content()
                        await page.get_by_role('button', name=suggested_username).click()
                        user_data['username'] = suggested_username
                    
                    # Click Next
                    await page.get_by_role('button', name='Next').click()
                    await self._random_delay()
                    
                    # Enter password
                    await page.locator('input[type="password"]').first.fill(user_data['password'])
                    await self._random_delay()
                    
                    # Confirm password
                    await page.locator('input[type="password"]').last.fill(user_data['password'])
                    await self._random_delay()
                    
                    # Click Next
                    await page.get_by_role('button', name='Next').click()
                    await self._take_screenshot(page, account_id, "step4")
                    await self._random_delay()
                    
                    # Check for captcha
                    captcha_frame = page.frame_locator('iframe[title="reCAPTCHA"]')
                    if await captcha_frame.is_visible():
                        logging.warning("Captcha detected")
                        await captcha_frame.screenshot(path=str(self.config.SCREENSHOTS_DIR / f"account_{account_id}_captcha.png"))
                        return {
                            "success": False,
                            "error": "Captcha detected",
                            "user_data": user_data,
                            "timestamp": datetime.now().isoformat()
                        }
                    
                    # Check for success
                    if await page.get_by_text('Welcome to Google').is_visible():
                        email = f"{user_data['username']}@gmail.com"
                        result = {
                            "success": True,
                            "email": email,
                            "user_data": user_data,
                            "proxy_used": browser_profile.get('proxy'),
                            "timestamp": datetime.now().isoformat(),
                            "screenshots_path": str(self.config.SCREENSHOTS_DIR / f"account_{account_id}")
                        }
                    else:
                        result = {
                            "success": False,
                            "error": "Registration failed or blocked",
                            "user_data": user_data,
                            "timestamp": datetime.now().isoformat()
                        }
                    
                except Exception as e:
                    logging.error(f"Error during automation: {str(e)}")
                    await self._take_screenshot(page, account_id, "error")
                    result = {
                        "success": False,
                        "error": str(e),
                        "user_data": user_data,
                        "timestamp": datetime.now().isoformat()
                    }
                
                finally:
                    await context.close()
                    await browser.close()
                
                await self._save_account_data(result)
                return result

        except Exception as e:
            logging.error(f"An error occurred during browser automation: {e}")
            return {
                "success": False,
                "error": str(e),
                "user_data": user_data,
                "timestamp": datetime.now().isoformat()
            }
    
    async def _random_delay(self, min_delay: float = 1.0, max_delay: float = 3.0):
        """Add random delay between actions to simulate human behavior"""
        delay = random.uniform(min_delay, max_delay)
        await asyncio.sleep(delay)
    
    async def _take_screenshot(self, page: Page, account_id: int, step: str):
        """Take screenshot of current page state"""
        screenshot_path = self.config.SCREENSHOTS_DIR / f"account_{account_id}_{step}.png"
        await page.screenshot(path=str(screenshot_path))
        logging.info(f"Screenshot saved: {screenshot_path}")

    async def register_batch_accounts(self, num_accounts: int, max_concurrent: int = None) -> List[Dict[str, Any]]:
        """
        Đăng ký nhiều tài khoản cùng lúc
        
        Args:
            num_accounts: Số lượng tài khoản cần đăng ký
            max_concurrent: Số lượng tối đa chạy đồng thời
            
        Returns:
            List kết quả đăng ký
        """
        if max_concurrent is None:
            max_concurrent = self.config.MAX_CONCURRENT_REGISTRATIONS
            
        print(f"\n🚀 Starting batch registration of {num_accounts} accounts")
        print(f"⚙️ Max concurrent: {max_concurrent}")
        print(f"⏱️ Delay between registrations: {self.config.DELAY_BETWEEN_REGISTRATIONS}s")
        
        # Create semaphore to limit concurrent registrations
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def register_with_semaphore(account_id: int):
            async with semaphore:
                result = await self.register_single_account(account_id)
                
                # Delay between registrations
                if account_id < num_accounts:
                    delay = self.config.DELAY_BETWEEN_REGISTRATIONS
                    print(f"⏰ Waiting {delay}s before next registration...")
                    await asyncio.sleep(delay)
                
                return result
        
        # Create tasks for all accounts
        tasks = []
        for i in range(1, num_accounts + 1):
            tasks.append(register_with_semaphore(i))
            
        # Run all tasks concurrently and wait for completion
        results = await asyncio.gather(*tasks)
        
        # Save batch results
        await self._save_batch_results(results)
        
        return results
    
    async def _save_account_data(self, account_data: Dict[str, Any]):
        """Save account data to JSON file"""
        try:
            # Create filename with timestamp and account ID
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            account_id = account_data.get("account_id", "unknown")
            filename = f"account_{account_id}_{timestamp}.json"
            
            # Save to accounts directory
            filepath = self.config.ACCOUNTS_DIR / filename
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(account_data, f, indent=2, ensure_ascii=False)
                
            print(f"💾 Account data saved to {filepath}")
            
        except Exception as e:
            print(f"⚠️ Failed to save account data: {e}")
    
    async def _save_batch_results(self, results: List[Dict[str, Any]]):
        """Save batch results to JSON file"""
        try:
            # Create filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"batch_results_{timestamp}.json"
            
            # Save to results directory
            filepath = self.config.RESULTS_DIR / filename
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
                
            print(f"💾 Batch results saved to {filepath}")
            
        except Exception as e:
            print(f"⚠️ Failed to save batch results: {e}")


@click.command()
@click.option('--accounts', '-n', default=1, help='Number of accounts to register')
@click.option('--concurrent', '-c', default=None, type=int, help='Max concurrent registrations')
@click.option('--test-mode', '-t', is_flag=True, help='Run in test mode (no actual registration)')
@click.option('--config-check', is_flag=True, help='Check configuration and exit')
@click.option('--proxy-test', is_flag=True, help='Test proxies and exit')
def main(accounts: int, concurrent: int, test_mode: bool, config_check: bool, proxy_test: bool):
    """Gmail Auto Registration Tool"""
    
    async def run_async():
        orchestrator = GmailRegistrationOrchestrator()
        
        # Initialize system
        if not await orchestrator.initialize():
            sys.exit(1)
            
        # Handle special modes
        if config_check:
            print("✅ Configuration check passed")
            return
            
        if proxy_test:
            if not orchestrator.proxy_manager:
                print("❌ Proxy testing requires proxy support to be enabled")
                return
            await orchestrator.proxy_manager.test_all_proxies()
            return
            
        if test_mode:
            print("🧪 Running in TEST MODE - no actual registrations will be performed")
            
        # Start registration process
        if accounts > 1:
            results = await orchestrator.register_batch_accounts(accounts, concurrent)
            success_count = len([r for r in results if r["success"]])
            print(f"\n✨ Batch registration completed: {success_count}/{accounts} successful")
        else:
            result = await orchestrator.register_single_account()
            if result["success"]:
                print("\n✨ Registration completed successfully!")
            else:
                print("\n❌ Registration failed!")
                sys.exit(1)
    
    # Run async code
    asyncio.run(run_async())


if __name__ == "__main__":
    main()