{"success": false, "error": "Locator.fill: Timeout 30000ms exceeded.\nCall log:\n  - waiting for locator(\"input[type=\\\"email\\\"]\")\n", "user_data": {"first_name": "<PERSON>", "last_name": "<PERSON>", "full_name": "<PERSON>", "username": "jacob.jones", "password": "pDiMt@s!UZ4J", "birth_date": "2005-12-11", "birth_year": 2005, "birth_month": 12, "birth_day": 11, "phone": "+19725779617", "recovery_email": "<EMAIL>", "gender": "Male", "city": "Boston", "timezone": "America/Honolulu", "generated_at": "2025-07-09T01:19:05.518415"}, "timestamp": "2025-07-09T01:20:02.824673"}